<script lang="ts" setup>
import type { UserInfoData } from '#/api/core/user-info';
import type { CloneSetConfig } from '#/api/core/digital-human';

import { computed, onMounted, ref } from 'vue';

import { Page } from '@vben/common-ui';

import {
  Avatar,
  Card,
  Col,
  Descriptions,
  Row,
  Spin,
  Statistic,
  message,
} from 'ant-design-vue';

import { getUserInfoApi, getCloneSetConfig } from '#/api/core/user-info';

defineOptions({ name: 'UserProfile' });

// 响应式数据
const loading = ref(false);
const userInfo = ref<UserInfoData | null>(null);
const cloneConfig = ref<CloneSetConfig | null>(null);

// 计算合成视频显示值
const videoTimeDisplay = computed(() => {
  if (!userInfo.value) return '0';
  
  if (userInfo.value.second_infinite === 1) {
    return '无限';
  }
  
  if (userInfo.value.second) {
    return (userInfo.value.second / 60).toFixed(2);
  }
  
  return '0';
});

// 是否显示高保真相关功能
const showHighFidelity = computed(() => {
  return cloneConfig.value?.voice_high_open === 1;
});

// 是否显示专业版相关功能
const showProfessional = computed(() => {
  return cloneConfig.value?.xunfei_sound_clone_swich === 1;
});

// 获取用户信息
const fetchUserInfo = async () => {
  try {
    loading.value = true;
    
    // 并行获取用户信息和配置信息
    const [userInfoData, configData] = await Promise.all([
      getUserInfoApi(),
      getCloneSetConfig(),
    ]);
    
    userInfo.value = userInfoData;
    cloneConfig.value = configData;
    
    console.log('用户信息获取成功:', userInfoData);
    console.log('配置信息获取成功:', configData);
  } catch (error) {
    console.error('获取用户信息失败:', error);
    message.error('获取用户信息失败，请重试');
  } finally {
    loading.value = false;
  }
};

// 页面初始化
onMounted(() => {
  fetchUserInfo();
});
</script>

<template>
  <Page
    description="查看和管理您的个人信息"
    title="个人信息"
  >
    <Spin :spinning="loading" tip="加载中...">
      <div class="user-profile-container">
        <!-- 用户基本信息卡片 -->
        <Card class="user-basic-info" title="基本信息">
          <div class="user-info-header">
            <Avatar
              :size="80"
              :src="userInfo?.avatar"
              class="user-avatar"
            >
              {{ userInfo?.nickname?.charAt(0) || '用' }}
            </Avatar>
            <div class="user-details">
              <h2 class="user-name">{{ userInfo?.nickname || '用户' }}</h2>
              <p class="user-phone">{{ userInfo?.telphone || '未绑定手机' }}</p>
              <p class="user-balance">
                余额：<span class="balance-amount">{{ userInfo?.balance || '0.00' }}</span> 点
              </p>
            </div>
          </div>
        </Card>

        <!-- 统计数据卡片 -->
        <Card class="statistics-card" title="使用统计">
          <Row :gutter="[16, 16]">
            <Col :xs="12" :sm="8" :md="6" :lg="4">
              <Statistic
                title="AI文案次数"
                :value="userInfo?.ai_copywriting_times || 0"
                suffix="次"
              />
            </Col>
            <Col :xs="12" :sm="8" :md="6" :lg="4">
              <Statistic
                title="合成视频"
                :value="videoTimeDisplay"
                suffix="分"
              />
            </Col>
            <Col v-if="showHighFidelity" :xs="12" :sm="8" :md="6" :lg="4">
              <Statistic
                title="高保真声音"
                :value="userInfo?.voice_twin_count || 0"
                suffix="次"
              />
            </Col>
            <Col v-if="showHighFidelity" :xs="12" :sm="8" :md="6" :lg="4">
              <Statistic
                title="高保真合成"
                :value="userInfo?.high_fidelity_words_number || 0"
                suffix="字"
              />
            </Col>
            <Col v-if="showProfessional" :xs="12" :sm="8" :md="6" :lg="4">
              <Statistic
                title="专业版声音"
                :value="userInfo?.xunfei_sound_clone_words_number || 0"
                suffix="次"
              />
            </Col>
            <Col v-if="showProfessional" :xs="12" :sm="8" :md="6" :lg="4">
              <Statistic
                title="专业版合成"
                :value="userInfo?.xunfei_sound_generate_words_number || 0"
                suffix="字"
              />
            </Col>
          </Row>
        </Card>

        <!-- 详细信息卡片 -->
        <Card class="details-card" title="详细信息">
          <Descriptions :column="{ xs: 1, sm: 2, md: 3 }" bordered>
            <Descriptions.Item label="用户ID">
              {{ userInfo?.id || '-' }}
            </Descriptions.Item>
            <Descriptions.Item label="注册时间">
              {{ userInfo?.create_time || '-' }}
            </Descriptions.Item>
            <Descriptions.Item label="最后更新">
              {{ userInfo?.update_time || '-' }}
            </Descriptions.Item>
            <Descriptions.Item label="分销等级">
              {{ userInfo?.distribution_level || 0 }}
            </Descriptions.Item>
            <Descriptions.Item label="佣金">
              {{ userInfo?.brokerage || '0.00' }} 元
            </Descriptions.Item>
            <Descriptions.Item label="是否会员">
              {{ userInfo?.is_member ? '是' : '否' }}
            </Descriptions.Item>
          </Descriptions>
        </Card>
      </div>
    </Spin>
  </Page>
</template>

<style scoped>
.user-profile-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.user-basic-info {
  background: var(--vben-color-bg-container);
  border: 1px solid var(--vben-color-border);
}

.user-info-header {
  display: flex;
  align-items: center;
  gap: 24px;
}

.user-avatar {
  flex-shrink: 0;
  background: var(--vben-color-primary);
  color: white;
}

.user-details {
  flex: 1;
}

.user-name {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--vben-color-text);
}

.user-phone {
  margin: 0 0 8px 0;
  color: var(--vben-color-text-secondary);
  font-size: 14px;
}

.user-balance {
  margin: 0;
  color: var(--vben-color-text-secondary);
  font-size: 14px;
}

.balance-amount {
  color: var(--vben-color-primary);
  font-weight: 600;
  font-size: 16px;
}

.statistics-card,
.details-card {
  background: var(--vben-color-bg-container);
  border: 1px solid var(--vben-color-border);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-info-header {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }
  
  .user-name {
    font-size: 20px;
  }
}

/* 暗黑模式适配 */
@media (prefers-color-scheme: dark) {
  .user-basic-info,
  .statistics-card,
  .details-card {
    background: var(--vben-color-bg-container);
    border-color: var(--vben-color-border);
  }
}
</style>
