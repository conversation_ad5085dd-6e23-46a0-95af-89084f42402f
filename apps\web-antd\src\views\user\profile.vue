<script lang="ts" setup>
import type { UserInfoData } from '#/api/core/user-info';
import type { CloneSetConfig } from '#/api/core/digital-human';

import { computed, onMounted, ref } from 'vue';

import {
  Avatar,
  Card,
  Spin,
  message,
} from 'ant-design-vue';

import { getUserInfoApi, getCloneSetConfig } from '#/api/core/user-info';

defineOptions({ name: 'UserProfile' });

// 响应式数据
const loading = ref(false);
const userInfo = ref<UserInfoData | null>(null);
const cloneConfig = ref<CloneSetConfig | null>(null);

// 计算合成视频显示值
const videoTimeDisplay = computed(() => {
  if (!userInfo.value) return '0';
  
  if (userInfo.value.second_infinite === 1) {
    return '无限';
  }
  
  if (userInfo.value.second) {
    return (userInfo.value.second / 60).toFixed(2);
  }
  
  return '0';
});

// 是否显示高保真相关功能
const showHighFidelity = computed(() => {
  return cloneConfig.value?.voice_high_open === 1;
});

// 是否显示专业版相关功能
const showProfessional = computed(() => {
  return cloneConfig.value?.xunfei_sound_clone_swich === 1;
});

// 获取用户信息
const fetchUserInfo = async () => {
  try {
    loading.value = true;
    
    // 并行获取用户信息和配置信息
    const [userInfoData, configData] = await Promise.all([
      getUserInfoApi(),
      getCloneSetConfig(),
    ]);
    
    userInfo.value = userInfoData;
    cloneConfig.value = configData;
    
    console.log('用户信息获取成功:', userInfoData);
    console.log('配置信息获取成功:', configData);
  } catch (error) {
    console.error('获取用户信息失败:', error);
    message.error('获取用户信息失败，请重试');
  } finally {
    loading.value = false;
  }
};

// 页面初始化
onMounted(() => {
  fetchUserInfo();
});
</script>

<template>
  <div class="user-profile-page">
    <Spin :spinning="loading" tip="加载中...">
      <!-- 用户信息卡片 - 左上角 -->
      <Card class="user-info-card">
        <div class="user-info-content">
          <!-- 用户基本信息 -->
          <div class="user-basic-section">
            <Avatar
              :size="60"
              :src="userInfo?.avatar"
              class="user-avatar"
            >
              {{ userInfo?.nickname?.charAt(0) || '用' }}
            </Avatar>
            <div class="user-details">
              <h3 class="user-name">{{ userInfo?.nickname || '用户' }}</h3>
              <p class="user-balance">
                余额：<span class="balance-amount">{{ userInfo?.balance || '0.00' }}</span> 点
              </p>
            </div>
          </div>

          <!-- 使用统计 -->
          <div class="statistics-section">
            <div class="stat-item">
              <span class="stat-label">AI文案次数</span>
              <span class="stat-value">{{ userInfo?.ai_copywriting_times || 0 }}次</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">合成视频</span>
              <span class="stat-value">{{ videoTimeDisplay }}分</span>
            </div>
            <div v-if="showHighFidelity" class="stat-item">
              <span class="stat-label">高保真声音</span>
              <span class="stat-value">{{ userInfo?.voice_twin_count || 0 }}次</span>
            </div>
            <div v-if="showHighFidelity" class="stat-item">
              <span class="stat-label">高保真合成</span>
              <span class="stat-value">{{ userInfo?.high_fidelity_words_number || 0 }}字</span>
            </div>
            <div v-if="showProfessional" class="stat-item">
              <span class="stat-label">专业版声音</span>
              <span class="stat-value">{{ userInfo?.xunfei_sound_clone_words_number || 0 }}次</span>
            </div>
            <div v-if="showProfessional" class="stat-item">
              <span class="stat-label">专业版合成</span>
              <span class="stat-value">{{ userInfo?.xunfei_sound_generate_words_number || 0 }}字</span>
            </div>
          </div>
        </div>
      </Card>
    </Spin>
  </div>
</template>

<style scoped>
.user-profile-page {
  padding: 24px;
  min-height: 100vh;
  background: var(--vben-color-bg-layout);
}

.user-info-card {
  position: absolute;
  top: 24px;
  left: 24px;
  width: 320px;
  background: var(--vben-color-bg-container);
  border: 1px solid var(--vben-color-border);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.user-info-content {
  padding: 16px;
}

.user-basic-section {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--vben-color-border);
}

.user-avatar {
  flex-shrink: 0;
  background: var(--vben-color-primary);
  color: white;
}

.user-details {
  flex: 1;
}

.user-name {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--vben-color-text);
}

.user-balance {
  margin: 0;
  color: var(--vben-color-text-secondary);
  font-size: 14px;
}

.balance-amount {
  color: var(--vben-color-primary);
  font-weight: 600;
  font-size: 16px;
}

.statistics-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-label {
  font-size: 12px;
  color: var(--vben-color-text-secondary);
  line-height: 1.2;
}

.stat-value {
  font-size: 14px;
  font-weight: 600;
  color: var(--vben-color-text);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-info-card {
    position: relative;
    top: 0;
    left: 0;
    width: 100%;
    margin-bottom: 24px;
  }

  .user-profile-page {
    padding: 16px;
  }

  .statistics-section {
    grid-template-columns: 1fr;
  }
}

/* 暗黑模式适配 */
@media (prefers-color-scheme: dark) {
  .user-info-card {
    background: var(--vben-color-bg-container);
    border-color: var(--vben-color-border);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }

  .user-basic-section {
    border-bottom-color: var(--vben-color-border);
  }
}
</style>
